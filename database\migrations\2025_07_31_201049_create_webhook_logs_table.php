<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_deliveries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('webhook_id')->constrained()->onDelete('cascade');
            $table->string('event_type'); // booking.paid, booking.cancelled, etc
            $table->json('payload'); // Original payload data
            $table->text('encrypted_payload'); // Encrypted payload
            $table->string('signature'); // HMAC signature
            $table->enum('status', ['pending', 'delivered', 'failed', 'retrying'])->default('pending');
            $table->integer('response_status')->nullable(); // HTTP response status
            $table->text('response_body')->nullable(); // Response body from webhook endpoint
            $table->integer('attempt_count')->default(0); // Number of delivery attempts
            $table->timestamp('next_retry_at')->nullable(); // When to retry next
            $table->timestamp('delivered_at')->nullable(); // When successfully delivered
            $table->timestamp('failed_at')->nullable(); // When permanently failed
            $table->text('error_message')->nullable(); // Error message if failed
            $table->timestamps();

            $table->index(['webhook_id', 'status']);
            $table->index(['event_type']);
            $table->index(['next_retry_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_deliveries');
    }
};
