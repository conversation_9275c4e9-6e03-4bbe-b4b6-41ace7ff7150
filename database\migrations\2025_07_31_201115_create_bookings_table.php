<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('booking_code')->unique(); // Kode booking unik
            $table->string('passenger_name'); // Nama penumpang
            $table->string('passenger_email'); // Email penumpang
            $table->string('flight_number'); // Nomor penerbangan
            $table->string('departure_city'); // Kota keberangkatan
            $table->string('arrival_city'); // Kota tujuan
            $table->datetime('departure_time'); // Waktu keberangkatan
            $table->datetime('arrival_time'); // Waktu kedatangan
            $table->decimal('price', 10, 2); // Harga tiket
            $table->enum('status', ['pending', 'paid', 'cancelled', 'refunded'])->default('pending');
            $table->timestamp('paid_at')->nullable(); // Waktu pembayaran
            $table->timestamp('cancelled_at')->nullable(); // Waktu pembatalan
            $table->text('cancellation_reason')->nullable(); // Alasan pembatalan
            $table->timestamps();

            $table->index(['status']);
            $table->index(['booking_code']);
            $table->index(['passenger_email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
