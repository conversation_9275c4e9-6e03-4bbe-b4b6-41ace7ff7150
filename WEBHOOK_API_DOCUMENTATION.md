# Webhook API Documentation

## Overview

Sistem Webhook API untuk notifikasi perubahan status pemesanan tiket pesawat. API ini memungkinkan pihak ketiga untuk mendaftarkan URL webhook mereka dan menerima notifikasi real-time ketika ada perubahan status booking.

## Features

- **Webhook Registration**: Daftarkan URL webhook untuk menerima notifikasi
- **Event Subscription**: Subscribe ke event tertentu (booking.paid, booking.cancelled, booking.refunded)
- **Signature Verification**: Verifikasi integritas data dengan HMAC SHA-256
- **Data Encryption**: Semua payload dienkripsi menggunakan Laravel encryption
- **Retry Mechanism**: Automatic retry dengan exponential backoff strategy
- **Delivery Tracking**: Track status pengiriman webhook

## Authentication

Saat ini API tidak memerlukan authentication, namun untuk production disarankan menggunakan API key atau token authentication.

## Base URL

```
http://localhost:8000/api
```

## Webhook Management Endpoints

### 1. Register Webhook

**POST** `/webhooks`

Mendaftarkan webhook baru.

**Request Body:**
```json
{
    "name": "My Webhook",
    "url": "https://example.com/webhook",
    "events": ["booking.paid", "booking.cancelled"],
    "secret_key": "optional_custom_secret_key",
    "max_retries": 3,
    "retry_interval": 300,
    "headers": {
        "Authorization": "Bearer token123"
    }
}
```

**Response:**
```json
{
    "message": "Webhook registered successfully",
    "webhook": {
        "id": 1,
        "name": "My Webhook",
        "url": "https://example.com/webhook",
        "events": ["booking.paid", "booking.cancelled"],
        "secret_key": "generated_secret_key",
        "is_active": true,
        "max_retries": 3,
        "retry_interval": 300,
        "headers": {
            "Authorization": "Bearer token123"
        },
        "created_at": "2025-07-31T20:00:00.000000Z",
        "updated_at": "2025-07-31T20:00:00.000000Z"
    }
}
```

### 2. List Webhooks

**GET** `/webhooks`

Mendapatkan daftar webhook yang terdaftar.

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "My Webhook",
            "url": "https://example.com/webhook",
            "events": ["booking.paid", "booking.cancelled"],
            "is_active": true,
            "last_delivery_at": "2025-07-31T20:00:00.000000Z",
            "last_delivery_status": "success",
            "created_at": "2025-07-31T20:00:00.000000Z"
        }
    ],
    "links": {...},
    "meta": {...}
}
```

### 3. Get Webhook Details

**GET** `/webhooks/{id}`

Mendapatkan detail webhook tertentu.

### 4. Update Webhook

**PUT** `/webhooks/{id}`

Update konfigurasi webhook.

### 5. Delete Webhook

**DELETE** `/webhooks/{id}`

Hapus webhook.

### 6. Test Webhook

**POST** `/webhooks/{id}/test`

Test webhook endpoint dengan mengirim test payload.

### 7. Get Webhook Deliveries

**GET** `/webhooks/{id}/deliveries`

Mendapatkan history pengiriman webhook.

### 8. Get Webhook Statistics

**GET** `/webhooks/{id}/stats`

Mendapatkan statistik webhook (total deliveries, success rate, dll).

### 9. Retry Failed Delivery

**POST** `/deliveries/{delivery_id}/retry`

Retry pengiriman webhook yang gagal.

## Webhook Events

### Available Events

1. **booking.paid** - Triggered when booking is marked as paid
2. **booking.cancelled** - Triggered when booking is cancelled
3. **booking.refunded** - Triggered when booking is refunded

### Webhook Payload Structure

Ketika event terjadi, sistem akan mengirim HTTP POST request ke URL webhook yang terdaftar dengan struktur berikut:

**Headers:**
```
Content-Type: application/json
X-Webhook-Signature: sha256=<signature>
X-Webhook-Event: <event_type>
X-Webhook-Delivery: <delivery_id>
User-Agent: FlightBooking-Webhook/1.0
```

**Body:**
```json
{
    "event_type": "booking.paid",
    "data": "<encrypted_payload>",
    "timestamp": "2025-07-31T20:00:00.000000Z"
}
```

**Decrypted Payload:**
```json
{
    "event_type": "booking.paid",
    "booking": {
        "id": 1,
        "booking_code": "**********",
        "passenger_name": "John Doe",
        "passenger_email": "<EMAIL>",
        "flight_number": "GA123",
        "departure_city": "Jakarta",
        "arrival_city": "Bali",
        "departure_time": "2025-08-01T10:00:00.000000Z",
        "arrival_time": "2025-08-01T12:00:00.000000Z",
        "price": "1500000.00",
        "status": "paid",
        "previous_status": "pending",
        "paid_at": "2025-07-31T20:00:00.000000Z",
        "cancelled_at": null,
        "cancellation_reason": null
    },
    "timestamp": "2025-07-31T20:00:00.000000Z"
}
```

## Security

### Signature Verification

Setiap webhook request disertai dengan signature di header `X-Webhook-Signature`. Untuk memverifikasi:

```php
$signature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'];
$payload = file_get_contents('php://input');
$secret = 'your_webhook_secret_key';

$expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

if (!hash_equals($expectedSignature, $signature)) {
    // Invalid signature
    http_response_code(401);
    exit('Unauthorized');
}
```

### Data Decryption

Payload data dienkripsi menggunakan Laravel encryption. Untuk mendekripsi:

```php
use Illuminate\Support\Facades\Crypt;

$encryptedData = $requestData['data'];
$decryptedPayload = json_decode(Crypt::decryptString($encryptedData), true);
```

## Retry Mechanism

- **Max Retries**: Default 3 kali, dapat dikonfigurasi per webhook
- **Retry Interval**: Default 5 menit (300 detik), dapat dikonfigurasi
- **Backoff Strategy**: Exponential backoff (5 min, 10 min, 20 min)
- **Retry Conditions**: HTTP status 4xx atau 5xx, connection timeout, atau network error

## Response Requirements

Webhook endpoint harus merespons dengan:
- **Success**: HTTP status 200-299
- **Failure**: HTTP status 400-599 (akan di-retry)

## Testing Endpoints

### Create Test Booking

**POST** `/bookings`

```json
{
    "passenger_name": "John Doe",
    "passenger_email": "<EMAIL>",
    "flight_number": "GA123",
    "departure_city": "Jakarta",
    "arrival_city": "Bali",
    "departure_time": "2025-08-01T10:00:00Z",
    "arrival_time": "2025-08-01T12:00:00Z",
    "price": 1500000
}
```

### Pay Booking (Trigger webhook)

**POST** `/bookings/{id}/pay`

### Cancel Booking (Trigger webhook)

**POST** `/bookings/{id}/cancel`

```json
{
    "reason": "Customer request"
}
```

## Error Handling

API menggunakan standard HTTP status codes:

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **404**: Not Found
- **422**: Validation Error
- **500**: Internal Server Error

Error response format:
```json
{
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    }
}
```
