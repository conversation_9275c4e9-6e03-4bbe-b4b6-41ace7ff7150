# Flight Booking Webhook System

Sistem Webhook API untuk notifikasi perubahan status pemesanan tiket pesawat yang memungkinkan pihak ketiga untuk menerima notifikasi real-time ketika ada perubahan status booking.

## Features

- ✅ **Webhook Registration**: Endpoint untuk mendaftarkan URL webhook pihak ketiga
- ✅ **Event Subscription**: Subscribe ke event tertentu (booking.paid, booking.cancelled, booking.refunded)
- ✅ **Signature Verification**: Verifikasi integritas data dengan HMAC SHA-256
- ✅ **Data Encryption**: Semua payload dienkripsi menggunakan algoritma yang aman
- ✅ **Retry Mechanism**: Automatic retry hingga 3 kali dengan exponential backoff strategy (5 menit interval)
- ✅ **Delivery Tracking**: Track status pengiriman webhook dan history
- ✅ **Management API**: CRUD operations untuk webhook management

## Tech Stack

- **Framework**: <PERSON><PERSON> 11
- **Database**: SQLite (default), MySQL/PostgreSQL supported
- **Queue**: Database queue untuk async webhook delivery
- **Encryption**: Laravel built-in encryption
- **HTTP Client**: Laravel HTTP Client untuk webhook delivery

## Installation & Setup

### 1. Install Dependencies

```bash
composer install
```

### 2. Environment Setup

```bash
cp .env.example .env
php artisan key:generate
```

### 3. Database Setup

```bash
# Create database (SQLite is default)
touch database/database.sqlite

# Run migrations
php artisan migrate
```

### 4. Queue Setup

```bash
# Start queue worker for webhook delivery
php artisan queue:work
```

### 5. Start Development Server

```bash
php artisan serve
```

Server akan berjalan di `http://localhost:8000`

## Quick Start

### 1. Register Webhook

```bash
curl -X POST http://localhost:8000/api/webhooks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Webhook",
    "url": "https://webhook.site/your-unique-id",
    "events": ["booking.paid", "booking.cancelled"]
  }'
```

### 2. Create Test Booking

```bash
curl -X POST http://localhost:8000/api/bookings \
  -H "Content-Type: application/json" \
  -d '{
    "passenger_name": "John Doe",
    "passenger_email": "<EMAIL>",
    "flight_number": "GA123",
    "departure_city": "Jakarta",
    "arrival_city": "Bali",
    "departure_time": "2025-08-01T10:00:00Z",
    "arrival_time": "2025-08-01T12:00:00Z",
    "price": 1500000
  }'
```

### 3. Trigger Webhook (Pay Booking)

```bash
curl -X POST http://localhost:8000/api/bookings/1/pay
```

Webhook akan otomatis dikirim ke URL yang didaftarkan!

## Testing

### Automated Test

Jalankan test script yang disediakan:

```bash
php test_webhook_system.php
```

### Manual Testing

1. Buat webhook di [webhook.site](https://webhook.site) untuk mendapatkan URL test
2. Register webhook menggunakan URL tersebut
3. Buat booking dan ubah statusnya
4. Lihat webhook yang diterima di webhook.site

## API Documentation

Lihat dokumentasi lengkap di [WEBHOOK_API_DOCUMENTATION.md](WEBHOOK_API_DOCUMENTATION.md)

### Key Endpoints

- `POST /api/webhooks` - Register webhook
- `GET /api/webhooks` - List webhooks
- `POST /api/webhooks/{id}/test` - Test webhook
- `GET /api/webhooks/{id}/deliveries` - Get delivery history
- `POST /api/bookings` - Create booking
- `POST /api/bookings/{id}/pay` - Pay booking (triggers webhook)
- `POST /api/bookings/{id}/cancel` - Cancel booking (triggers webhook)

## Webhook Security

### Signature Verification

Setiap webhook request disertai signature di header `X-Webhook-Signature`:

```php
$signature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'];
$payload = file_get_contents('php://input');
$secret = 'your_webhook_secret_key';

$expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

if (!hash_equals($expectedSignature, $signature)) {
    http_response_code(401);
    exit('Unauthorized');
}
```

### Data Decryption

```php
use Illuminate\Support\Facades\Crypt;

$encryptedData = $requestData['data'];
$decryptedPayload = json_decode(Crypt::decryptString($encryptedData), true);
```

## Retry Mechanism

- **Max Retries**: 3 kali (configurable)
- **Interval**: 5 menit (configurable)
- **Backoff Strategy**: Exponential (5 min → 10 min → 20 min)
- **Auto Retry**: Otomatis untuk HTTP 4xx/5xx responses

### Manual Retry Processing

```bash
# Process pending retries manually
php artisan webhooks:process-retries
```

## Monitoring

### Webhook Statistics

```bash
curl http://localhost:8000/api/webhooks/1/stats
```

Response:
```json
{
    "total_deliveries": 10,
    "successful_deliveries": 8,
    "failed_deliveries": 2,
    "pending_deliveries": 0,
    "last_delivery_at": "2025-07-31T20:00:00.000000Z",
    "last_delivery_status": "success"
}
```

### Delivery History

```bash
curl http://localhost:8000/api/webhooks/1/deliveries
```

## Production Deployment

### 1. Environment Variables

```env
APP_ENV=production
APP_DEBUG=false
QUEUE_CONNECTION=redis  # Recommended for production
```

### 2. Queue Worker

```bash
# Use supervisor or similar process manager
php artisan queue:work --daemon
```

### 3. Scheduled Tasks

Add to crontab for retry processing:

```bash
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Booking       │───▶│  Event System    │───▶│  Webhook        │
│   Status Change │    │  (BookingStatus  │    │  Service        │
│                 │    │   Changed)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Third Party   │◀───│  HTTP Request    │◀───│  Queue Job      │
│   Webhook URL   │    │  (Encrypted +    │    │  (DeliverWeb    │
│                 │    │   Signed)        │    │   hookJob)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Redberry](https://redberry.international/laravel-development)**
- **[Active Logic](https://activelogic.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
