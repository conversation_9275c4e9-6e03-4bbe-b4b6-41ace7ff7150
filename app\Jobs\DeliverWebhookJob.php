<?php

namespace App\Jobs;

use App\Models\WebhookDelivery;
use App\Services\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DeliverWebhookJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public WebhookDelivery $delivery;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 1; // We handle retries manually

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(WebhookDelivery $delivery)
    {
        $this->delivery = $delivery;
    }

    /**
     * Execute the job.
     */
    public function handle(WebhookService $webhookService): void
    {
        // Check if delivery is still valid
        if ($this->delivery->status === WebhookDelivery::STATUS_DELIVERED) {
            Log::info('Webhook delivery already completed', [
                'delivery_id' => $this->delivery->id,
            ]);
            return;
        }

        // Check if webhook is still active
        if (!$this->delivery->webhook->is_active) {
            Log::info('Webhook is inactive, skipping delivery', [
                'webhook_id' => $this->delivery->webhook_id,
                'delivery_id' => $this->delivery->id,
            ]);
            return;
        }

        $webhookService->deliverWebhook($this->delivery);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook delivery job failed', [
            'delivery_id' => $this->delivery->id,
            'webhook_id' => $this->delivery->webhook_id,
            'error' => $exception->getMessage(),
        ]);

        $this->delivery->markAsFailed($exception->getMessage());
    }
}
