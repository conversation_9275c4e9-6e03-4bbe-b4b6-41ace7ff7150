<?php

namespace App\Listeners;

use App\Events\BookingStatusChanged;
use App\Services\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class BookingStatusChangedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected WebhookService $webhookService;

    /**
     * Create the event listener.
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle the event.
     */
    public function handle(BookingStatusChanged $event): void
    {
        try {
            Log::info('Booking status changed, triggering webhooks', [
                'booking_id' => $event->booking->id,
                'booking_code' => $event->booking->booking_code,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->booking->status,
            ]);

            // Send webhook notification
            $this->webhookService->sendWebhookNotification(
                $event->getEventType(),
                $event->getPayload()
            );
        } catch (\Exception $e) {
            Log::error('Failed to process booking status change for webhooks', [
                'booking_id' => $event->booking->id,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to ensure the event is retried
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(BookingStatusChanged $event, \Throwable $exception): void
    {
        Log::error('BookingStatusChangedListener failed permanently', [
            'booking_id' => $event->booking->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
