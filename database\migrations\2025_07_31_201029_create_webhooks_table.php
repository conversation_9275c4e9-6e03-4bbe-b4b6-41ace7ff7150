<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhooks', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama webhook untuk identifikasi
            $table->string('url'); // URL endpoint webhook
            $table->json('events'); // Array event yang di-subscribe (booking.paid, booking.cancelled, etc)
            $table->string('secret_key'); // Secret key untuk signature verification
            $table->boolean('is_active')->default(true); // Status aktif/nonaktif
            $table->integer('max_retries')->default(3); // Maksimal retry
            $table->integer('retry_interval')->default(300); // Interval retry dalam detik (5 menit)
            $table->json('headers')->nullable(); // Custom headers untuk webhook
            $table->timestamp('last_delivery_at')->nullable(); // Waktu pengiriman terakhir
            $table->string('last_delivery_status')->nullable(); // Status pengiriman terakhir
            $table->timestamps();

            $table->index(['is_active']);
            $table->index(['events']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhooks');
    }
};
