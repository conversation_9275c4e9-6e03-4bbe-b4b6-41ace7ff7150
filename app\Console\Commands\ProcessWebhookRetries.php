<?php

namespace App\Console\Commands;

use App\Jobs\DeliverWebhookJob;
use App\Models\WebhookDelivery;
use Illuminate\Console\Command;

class ProcessWebhookRetries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhooks:process-retries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending webhook delivery retries';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing webhook delivery retries...');

        $pendingRetries = WebhookDelivery::where('status', WebhookDelivery::STATUS_RETRYING)
            ->where('next_retry_at', '<=', now())
            ->whereHas('webhook', function ($query) {
                $query->where('is_active', true);
            })
            ->get();

        if ($pendingRetries->isEmpty()) {
            $this->info('No pending retries found.');
            return self::SUCCESS;
        }

        $this->info("Found {$pendingRetries->count()} pending retries.");

        $processed = 0;
        foreach ($pendingRetries as $delivery) {
            try {
                DeliverWebhookJob::dispatch($delivery);
                $processed++;

                $this->line("Dispatched retry for delivery ID: {$delivery->id}");
            } catch (\Exception $e) {
                $this->error("Failed to dispatch retry for delivery ID {$delivery->id}: {$e->getMessage()}");
            }
        }

        $this->info("Successfully dispatched {$processed} webhook delivery retries.");

        return self::SUCCESS;
    }
}
