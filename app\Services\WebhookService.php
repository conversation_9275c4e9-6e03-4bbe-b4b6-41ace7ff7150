<?php

namespace App\Services;

use App\Models\Webhook;
use App\Models\WebhookDelivery;
use App\Jobs\DeliverWebhookJob;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;

class WebhookService
{
    /**
     * Send webhook notification for an event
     */
    public function sendWebhookNotification(string $eventType, array $payload): void
    {
        $webhooks = Webhook::where('is_active', true)
            ->get()
            ->filter(function ($webhook) use ($eventType) {
                return $webhook->isSubscribedTo($eventType);
            });

        foreach ($webhooks as $webhook) {
            $this->createWebhookDelivery($webhook, $eventType, $payload);
        }
    }

    /**
     * Create webhook delivery record and dispatch job
     */
    protected function createWebhookDelivery(Webhook $webhook, string $eventType, array $payload): void
    {
        // Encrypt payload
        $encryptedPayload = $this->encryptPayload($payload);
        
        // Generate signature
        $signature = $webhook->generateSignature($encryptedPayload);

        $delivery = WebhookDelivery::create([
            'webhook_id' => $webhook->id,
            'event_type' => $eventType,
            'payload' => $payload,
            'encrypted_payload' => $encryptedPayload,
            'signature' => $signature,
            'status' => WebhookDelivery::STATUS_PENDING,
            'attempt_count' => 0,
        ]);

        // Dispatch job to deliver webhook
        DeliverWebhookJob::dispatch($delivery);
    }

    /**
     * Deliver webhook to endpoint
     */
    public function deliverWebhook(WebhookDelivery $delivery): bool
    {
        $webhook = $delivery->webhook;
        
        try {
            $delivery->increment('attempt_count');

            $headers = array_merge([
                'Content-Type' => 'application/json',
                'X-Webhook-Signature' => 'sha256=' . $delivery->signature,
                'X-Webhook-Event' => $delivery->event_type,
                'X-Webhook-Delivery' => $delivery->id,
                'User-Agent' => 'FlightBooking-Webhook/1.0',
            ], $webhook->headers ?? []);

            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->post($webhook->url, [
                    'event_type' => $delivery->event_type,
                    'data' => $delivery->encrypted_payload,
                    'timestamp' => now()->toISOString(),
                ]);

            if ($response->successful()) {
                $delivery->markAsDelivered($response->status(), $response->body());
                $webhook->update([
                    'last_delivery_at' => now(),
                    'last_delivery_status' => 'success',
                ]);
                
                Log::info('Webhook delivered successfully', [
                    'webhook_id' => $webhook->id,
                    'delivery_id' => $delivery->id,
                    'url' => $webhook->url,
                    'status' => $response->status(),
                ]);

                return true;
            } else {
                throw new \Exception('HTTP ' . $response->status() . ': ' . $response->body());
            }
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            
            Log::error('Webhook delivery failed', [
                'webhook_id' => $webhook->id,
                'delivery_id' => $delivery->id,
                'url' => $webhook->url,
                'attempt' => $delivery->attempt_count,
                'error' => $errorMessage,
            ]);

            if ($delivery->canRetry()) {
                $this->scheduleRetry($delivery);
            } else {
                $delivery->markAsFailed($errorMessage);
                $webhook->update([
                    'last_delivery_at' => now(),
                    'last_delivery_status' => 'failed',
                ]);
            }

            return false;
        }
    }

    /**
     * Schedule retry for failed webhook delivery
     */
    protected function scheduleRetry(WebhookDelivery $delivery): void
    {
        // Exponential backoff: 5 minutes * (2 ^ attempt_count)
        $delayMinutes = $delivery->webhook->retry_interval * pow(2, $delivery->attempt_count - 1);
        $nextRetryAt = now()->addMinutes($delayMinutes);

        $delivery->update([
            'status' => WebhookDelivery::STATUS_RETRYING,
            'next_retry_at' => $nextRetryAt,
        ]);

        // Dispatch retry job
        DeliverWebhookJob::dispatch($delivery)->delay($nextRetryAt);
    }

    /**
     * Encrypt payload using Laravel's encryption
     */
    protected function encryptPayload(array $payload): string
    {
        return Crypt::encryptString(json_encode($payload));
    }

    /**
     * Decrypt payload
     */
    public function decryptPayload(string $encryptedPayload): array
    {
        return json_decode(Crypt::decryptString($encryptedPayload), true);
    }

    /**
     * Verify webhook signature
     */
    public function verifySignature(string $payload, string $signature, string $secret): bool
    {
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Register new webhook
     */
    public function registerWebhook(array $data): Webhook
    {
        $data['secret_key'] = $data['secret_key'] ?? $this->generateSecretKey();
        
        return Webhook::create($data);
    }

    /**
     * Generate secret key for webhook
     */
    protected function generateSecretKey(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Test webhook endpoint
     */
    public function testWebhook(Webhook $webhook): array
    {
        $testPayload = [
            'event_type' => 'webhook.test',
            'data' => [
                'message' => 'This is a test webhook',
                'webhook_id' => $webhook->id,
                'timestamp' => now()->toISOString(),
            ],
        ];

        $encryptedPayload = $this->encryptPayload($testPayload);
        $signature = $webhook->generateSignature($encryptedPayload);

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-Webhook-Signature' => 'sha256=' . $signature,
                'X-Webhook-Event' => 'webhook.test',
                'User-Agent' => 'FlightBooking-Webhook/1.0',
            ])->timeout(10)->post($webhook->url, [
                'event_type' => 'webhook.test',
                'data' => $encryptedPayload,
                'timestamp' => now()->toISOString(),
            ]);

            return [
                'success' => $response->successful(),
                'status' => $response->status(),
                'response' => $response->body(),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
