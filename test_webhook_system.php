<?php

/**
 * Simple test script to verify webhook system functionality
 * Run this script to test the webhook system
 */

require_once 'vendor/autoload.php';

use Illuminate\Http\Client\Factory as HttpClient;

class WebhookSystemTest
{
    private $baseUrl = 'http://localhost:8000/api';
    private $http;

    public function __construct()
    {
        $this->http = new HttpClient();
    }

    public function runTests()
    {
        echo "🚀 Starting Webhook System Tests\n\n";

        try {
            // Test 1: Register a webhook
            echo "📝 Test 1: Register Webhook\n";
            $webhook = $this->registerWebhook();
            echo "✅ Webhook registered successfully (ID: {$webhook['id']})\n\n";

            // Test 2: Create a test booking
            echo "📝 Test 2: Create Test Booking\n";
            $booking = $this->createTestBooking();
            echo "✅ Booking created successfully (ID: {$booking['id']}, Code: {$booking['booking_code']})\n\n";

            // Test 3: Test webhook endpoint
            echo "📝 Test 3: Test Webhook Endpoint\n";
            $testResult = $this->testWebhookEndpoint($webhook['id']);
            echo "✅ Webhook test completed\n\n";

            // Test 4: Pay booking (should trigger webhook)
            echo "📝 Test 4: Pay Booking (Trigger Webhook)\n";
            $this->payBooking($booking['id']);
            echo "✅ Booking paid successfully - webhook should be triggered\n\n";

            // Test 5: Check webhook deliveries
            echo "📝 Test 5: Check Webhook Deliveries\n";
            $deliveries = $this->getWebhookDeliveries($webhook['id']);
            echo "✅ Found " . count($deliveries['data']) . " webhook deliveries\n\n";

            // Test 6: Cancel booking (should trigger another webhook)
            echo "📝 Test 6: Cancel Booking (Trigger Another Webhook)\n";
            $this->cancelBooking($booking['id']);
            echo "✅ Booking cancelled successfully - webhook should be triggered\n\n";

            // Test 7: Get webhook statistics
            echo "📝 Test 7: Get Webhook Statistics\n";
            $stats = $this->getWebhookStats($webhook['id']);
            echo "✅ Webhook stats retrieved:\n";
            echo "   - Total deliveries: {$stats['total_deliveries']}\n";
            echo "   - Successful: {$stats['successful_deliveries']}\n";
            echo "   - Failed: {$stats['failed_deliveries']}\n\n";

            echo "🎉 All tests completed successfully!\n";

        } catch (Exception $e) {
            echo "❌ Test failed: " . $e->getMessage() . "\n";
        }
    }

    private function registerWebhook()
    {
        $response = $this->http->post($this->baseUrl . '/webhooks', [
            'name' => 'Test Webhook',
            'url' => 'https://webhook.site/unique-id', // Replace with actual webhook.site URL
            'events' => ['booking.paid', 'booking.cancelled'],
            'max_retries' => 3,
            'retry_interval' => 300,
        ]);

        if (!$response->successful()) {
            throw new Exception('Failed to register webhook: ' . $response->body());
        }

        return $response->json()['webhook'];
    }

    private function createTestBooking()
    {
        $response = $this->http->post($this->baseUrl . '/bookings', [
            'passenger_name' => 'John Doe',
            'passenger_email' => '<EMAIL>',
            'flight_number' => 'GA123',
            'departure_city' => 'Jakarta',
            'arrival_city' => 'Bali',
            'departure_time' => '2025-08-01T10:00:00Z',
            'arrival_time' => '2025-08-01T12:00:00Z',
            'price' => 1500000,
        ]);

        if (!$response->successful()) {
            throw new Exception('Failed to create booking: ' . $response->body());
        }

        return $response->json()['booking'];
    }

    private function testWebhookEndpoint($webhookId)
    {
        $response = $this->http->post($this->baseUrl . "/webhooks/{$webhookId}/test");

        if (!$response->successful()) {
            throw new Exception('Failed to test webhook: ' . $response->body());
        }

        return $response->json()['result'];
    }

    private function payBooking($bookingId)
    {
        $response = $this->http->post($this->baseUrl . "/bookings/{$bookingId}/pay");

        if (!$response->successful()) {
            throw new Exception('Failed to pay booking: ' . $response->body());
        }

        return $response->json();
    }

    private function cancelBooking($bookingId)
    {
        $response = $this->http->post($this->baseUrl . "/bookings/{$bookingId}/cancel", [
            'reason' => 'Test cancellation',
        ]);

        if (!$response->successful()) {
            throw new Exception('Failed to cancel booking: ' . $response->body());
        }

        return $response->json();
    }

    private function getWebhookDeliveries($webhookId)
    {
        $response = $this->http->get($this->baseUrl . "/webhooks/{$webhookId}/deliveries");

        if (!$response->successful()) {
            throw new Exception('Failed to get webhook deliveries: ' . $response->body());
        }

        return $response->json();
    }

    private function getWebhookStats($webhookId)
    {
        $response = $this->http->get($this->baseUrl . "/webhooks/{$webhookId}/stats");

        if (!$response->successful()) {
            throw new Exception('Failed to get webhook stats: ' . $response->body());
        }

        return $response->json();
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $test = new WebhookSystemTest();
    $test->runTests();
} else {
    echo "This script should be run from command line.\n";
    echo "Usage: php test_webhook_system.php\n";
}
