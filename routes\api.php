<?php

use App\Http\Controllers\BookingController;
use App\Http\Controllers\WebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Webhook Management Routes
Route::prefix('webhooks')->group(function () {
    Route::get('/', [WebhookController::class, 'index']);
    Route::post('/', [WebhookController::class, 'store']);
    Route::get('/{webhook}', [WebhookController::class, 'show']);
    Route::put('/{webhook}', [WebhookController::class, 'update']);
    Route::delete('/{webhook}', [WebhookController::class, 'destroy']);
    Route::post('/{webhook}/test', [WebhookController::class, 'test']);
    Route::get('/{webhook}/deliveries', [WebhookController::class, 'deliveries']);
    Route::get('/{webhook}/stats', [WebhookController::class, 'stats']);
    Route::post('/deliveries/{delivery}/retry', [WebhookController::class, 'retryDelivery']);
});

// Booking Routes (for testing webhook triggers)
Route::prefix('bookings')->group(function () {
    Route::get('/', [BookingController::class, 'index']);
    Route::post('/', [BookingController::class, 'store']);
    Route::get('/{booking}', [BookingController::class, 'show']);
    Route::put('/{booking}', [BookingController::class, 'update']);
    Route::post('/{booking}/pay', [BookingController::class, 'pay']);
    Route::post('/{booking}/cancel', [BookingController::class, 'cancel']);
});
