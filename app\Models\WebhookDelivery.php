<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebhookDelivery extends Model
{
    protected $table = 'webhook_deliveries';

    protected $fillable = [
        'webhook_id',
        'event_type',
        'payload',
        'encrypted_payload',
        'signature',
        'status',
        'response_status',
        'response_body',
        'attempt_count',
        'next_retry_at',
        'delivered_at',
        'failed_at',
        'error_message',
    ];

    protected $casts = [
        'payload' => 'array',
        'next_retry_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    public const STATUS_PENDING = 'pending';
    public const STATUS_DELIVERED = 'delivered';
    public const STATUS_FAILED = 'failed';
    public const STATUS_RETRYING = 'retrying';

    /**
     * Get the webhook that owns this delivery
     */
    public function webhook(): BelongsTo
    {
        return $this->belongsTo(Webhook::class);
    }

    /**
     * Check if delivery can be retried
     */
    public function canRetry(): bool
    {
        return $this->status !== self::STATUS_DELIVERED
            && $this->attempt_count < $this->webhook->max_retries;
    }

    /**
     * Mark delivery as failed
     */
    public function markAsFailed(?string $errorMessage = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'failed_at' => now(),
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Mark delivery as delivered
     */
    public function markAsDelivered(int $responseStatus, ?string $responseBody = null): void
    {
        $this->update([
            'status' => self::STATUS_DELIVERED,
            'delivered_at' => now(),
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
        ]);
    }
}
