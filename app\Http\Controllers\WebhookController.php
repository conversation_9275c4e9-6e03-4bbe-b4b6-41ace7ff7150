<?php

namespace App\Http\Controllers;

use App\Models\Webhook;
use App\Models\WebhookDelivery;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class WebhookController extends Controller
{
    protected WebhookService $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Display a listing of webhooks
     */
    public function index(): JsonResponse
    {
        $webhooks = Webhook::with(['deliveries' => function ($query) {
            $query->latest()->limit(5);
        }])->paginate(15);

        return response()->json($webhooks);
    }

    /**
     * Store a newly created webhook
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'events' => 'required|array|min:1',
            'events.*' => 'required|string|in:booking.paid,booking.cancelled,booking.refunded',
            'secret_key' => 'nullable|string|min:32|max:128',
            'max_retries' => 'nullable|integer|min:0|max:10',
            'retry_interval' => 'nullable|integer|min:60|max:3600', // 1 minute to 1 hour
            'headers' => 'nullable|array',
            'headers.*' => 'string',
        ]);

        try {
            $webhook = $this->webhookService->registerWebhook($validated);

            return response()->json([
                'message' => 'Webhook registered successfully',
                'webhook' => $webhook,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to register webhook',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified webhook
     */
    public function show(Webhook $webhook): JsonResponse
    {
        $webhook->load(['deliveries' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json($webhook);
    }

    /**
     * Update the specified webhook
     */
    public function update(Request $request, Webhook $webhook): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'url' => 'sometimes|url|max:500',
            'events' => 'sometimes|array|min:1',
            'events.*' => 'required|string|in:booking.paid,booking.cancelled,booking.refunded',
            'is_active' => 'sometimes|boolean',
            'max_retries' => 'sometimes|integer|min:0|max:10',
            'retry_interval' => 'sometimes|integer|min:60|max:3600',
            'headers' => 'nullable|array',
            'headers.*' => 'string',
        ]);

        $webhook->update($validated);

        return response()->json([
            'message' => 'Webhook updated successfully',
            'webhook' => $webhook,
        ]);
    }

    /**
     * Remove the specified webhook
     */
    public function destroy(Webhook $webhook): JsonResponse
    {
        $webhook->delete();

        return response()->json([
            'message' => 'Webhook deleted successfully',
        ]);
    }

    /**
     * Test webhook endpoint
     */
    public function test(Webhook $webhook): JsonResponse
    {
        $result = $this->webhookService->testWebhook($webhook);

        return response()->json([
            'message' => 'Webhook test completed',
            'result' => $result,
        ]);
    }

    /**
     * Get webhook deliveries
     */
    public function deliveries(Webhook $webhook): JsonResponse
    {
        $deliveries = $webhook->deliveries()
            ->latest()
            ->paginate(20);

        return response()->json($deliveries);
    }

    /**
     * Retry failed webhook delivery
     */
    public function retryDelivery(WebhookDelivery $delivery): JsonResponse
    {
        if ($delivery->status === WebhookDelivery::STATUS_DELIVERED) {
            return response()->json([
                'message' => 'Delivery already completed',
            ], 400);
        }

        if (!$delivery->webhook->is_active) {
            return response()->json([
                'message' => 'Webhook is inactive',
            ], 400);
        }

        // Reset delivery for retry
        $delivery->update([
            'status' => WebhookDelivery::STATUS_PENDING,
            'next_retry_at' => null,
            'error_message' => null,
        ]);

        $this->webhookService->deliverWebhook($delivery);

        return response()->json([
            'message' => 'Delivery retry initiated',
            'delivery' => $delivery->fresh(),
        ]);
    }

    /**
     * Get webhook statistics
     */
    public function stats(Webhook $webhook): JsonResponse
    {
        $stats = [
            'total_deliveries' => $webhook->deliveries()->count(),
            'successful_deliveries' => $webhook->deliveries()
                ->where('status', WebhookDelivery::STATUS_DELIVERED)->count(),
            'failed_deliveries' => $webhook->deliveries()
                ->where('status', WebhookDelivery::STATUS_FAILED)->count(),
            'pending_deliveries' => $webhook->deliveries()
                ->where('status', WebhookDelivery::STATUS_PENDING)->count(),
            'last_delivery_at' => $webhook->last_delivery_at,
            'last_delivery_status' => $webhook->last_delivery_status,
        ];

        return response()->json($stats);
    }
}
