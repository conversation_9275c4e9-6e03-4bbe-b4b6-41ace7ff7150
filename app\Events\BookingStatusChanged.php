<?php

namespace App\Events;

use App\Models\Booking;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Booking $booking;
    public string $previousStatus;

    /**
     * Create a new event instance.
     */
    public function __construct(Booking $booking, string $previousStatus)
    {
        $this->booking = $booking;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('booking.' . $this->booking->id),
        ];
    }

    /**
     * Get event type for webhook
     */
    public function getEventType(): string
    {
        return 'booking.' . $this->booking->status;
    }

    /**
     * Get payload for webhook
     */
    public function getPayload(): array
    {
        return [
            'event_type' => $this->getEventType(),
            'booking' => [
                'id' => $this->booking->id,
                'booking_code' => $this->booking->booking_code,
                'passenger_name' => $this->booking->passenger_name,
                'passenger_email' => $this->booking->passenger_email,
                'flight_number' => $this->booking->flight_number,
                'departure_city' => $this->booking->departure_city,
                'arrival_city' => $this->booking->arrival_city,
                'departure_time' => $this->booking->departure_time,
                'arrival_time' => $this->booking->arrival_time,
                'price' => $this->booking->price,
                'status' => $this->booking->status,
                'previous_status' => $this->previousStatus,
                'paid_at' => $this->booking->paid_at,
                'cancelled_at' => $this->booking->cancelled_at,
                'cancellation_reason' => $this->booking->cancellation_reason,
            ],
            'timestamp' => now()->toISOString(),
        ];
    }
}
