[2025-07-31 20:11:50] local.ERROR: Command "make:service" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:service\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('make:service')
#1 D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-07-31 20:12:04] local.ERROR: Command "make:service" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:service\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('make:service')
#1 D:\\test\\soal 4\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-07-31 20:13:47] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-31 20:14:05] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-31 20:14:05] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-31 20:14:09] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-31 20:14:10] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-31 20:14:17] local.ERROR: syntax error, unexpected token "--", expecting "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"--\", expecting \"{\" at D:\\test\\soal 4\\app\\Listeners\\BookingStatusChangedListener.php:5)
[stacktrace]
#0 D:\\test\\soal 4\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\test\\\\soal 4\\\\...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Listeners\\\\B...')
#2 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(69): ReflectionClass->__construct('App\\\\Listeners\\\\B...')
#3 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\DiscoverEvents.php(37): Illuminate\\Foundation\\Events\\DiscoverEvents::getListenerEvents(Object(Symfony\\Component\\Finder\\Finder), 'D:\\\\test\\\\soal 4')
#4 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Foundation\\Events\\DiscoverEvents::within(Array, 'D:\\\\test\\\\soal 4')
#5 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(780): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Support\\LazyCollection))
#6 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(155): Illuminate\\Support\\LazyCollection->pipe(Object(Closure))
#7 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(127): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoverEvents()
#8 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(113): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->discoveredEvents()
#9 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php(58): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->getEvents()
#10 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#11 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#14 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#15 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(127): Illuminate\\Container\\Container->call(Object(Closure))
#16 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1147): Illuminate\\Support\\ServiceProvider->callBootingCallbacks()
#17 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider))
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider), 'Illuminate\\\\Foun...')
#19 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#20 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#22 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#23 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#24 D:\\test\\soal 4\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\test\\soal 4\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
