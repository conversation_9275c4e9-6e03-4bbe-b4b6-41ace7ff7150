<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings
     */
    public function index(): JsonResponse
    {
        $bookings = Booking::latest()->paginate(15);
        return response()->json($bookings);
    }

    /**
     * Store a newly created booking
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'passenger_name' => 'required|string|max:255',
            'passenger_email' => 'required|email|max:255',
            'flight_number' => 'required|string|max:10',
            'departure_city' => 'required|string|max:100',
            'arrival_city' => 'required|string|max:100',
            'departure_time' => 'required|date',
            'arrival_time' => 'required|date|after:departure_time',
            'price' => 'required|numeric|min:0',
        ]);

        $validated['booking_code'] = 'BK' . strtoupper(Str::random(8));
        $validated['status'] = Booking::STATUS_PENDING;

        $booking = Booking::create($validated);

        return response()->json([
            'message' => 'Booking created successfully',
            'booking' => $booking,
        ], 201);
    }

    /**
     * Display the specified booking
     */
    public function show(Booking $booking): JsonResponse
    {
        return response()->json($booking);
    }

    /**
     * Update the specified booking
     */
    public function update(Request $request, Booking $booking): JsonResponse
    {
        $validated = $request->validate([
            'passenger_name' => 'sometimes|string|max:255',
            'passenger_email' => 'sometimes|email|max:255',
            'flight_number' => 'sometimes|string|max:10',
            'departure_city' => 'sometimes|string|max:100',
            'arrival_city' => 'sometimes|string|max:100',
            'departure_time' => 'sometimes|date',
            'arrival_time' => 'sometimes|date|after:departure_time',
            'price' => 'sometimes|numeric|min:0',
        ]);

        $booking->update($validated);

        return response()->json([
            'message' => 'Booking updated successfully',
            'booking' => $booking,
        ]);
    }

    /**
     * Mark booking as paid
     */
    public function pay(Booking $booking): JsonResponse
    {
        if ($booking->status !== Booking::STATUS_PENDING) {
            return response()->json([
                'message' => 'Booking cannot be paid. Current status: ' . $booking->status,
            ], 400);
        }

        $booking->markAsPaid();

        return response()->json([
            'message' => 'Booking marked as paid successfully',
            'booking' => $booking->fresh(),
        ]);
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, Booking $booking): JsonResponse
    {
        if ($booking->status === Booking::STATUS_CANCELLED) {
            return response()->json([
                'message' => 'Booking is already cancelled',
            ], 400);
        }

        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        $booking->cancel($validated['reason'] ?? null);

        return response()->json([
            'message' => 'Booking cancelled successfully',
            'booking' => $booking->fresh(),
        ]);
    }
}
